import type { FC, SVGProps } from "react";
import { createIcon } from "@panda-design/components";
import Close from "./Close";
import IconInfoflow from "./IconInfoflow";
import IconOutlook from "./IconOutlook";
import SendActived from "./SendActived";
import AiTools1 from "./AiTools1";
import AiTools2 from "./AiTools2";
import AiTools3 from "./AiTools3";
import AiTools4 from "./AiTools4";
import Alert from "./Alert";
import AnnounceAlert from "./AnnounceAlert";
import ArrowRight from "./ArrowRight";
import ArrowRight1 from "./ArrowRight1";
import CallCount from "./CallCount";
import Case from "./Case";
import Comment from "./Comment";
import Copy from "./Copy";
import Debug from "./Debug";
import Delete from "./Delete";
import Detail from "./Detail";
import Dev from "./Dev";
import DevBg from "./DevBg";
import DevBg1 from "./DevBg1";
import DevBg2 from "./DevBg2";
import DownOutlined from "./DownOutlined";
import Elipsis from "./Elipsis";
import ExitFullscreen from "./ExitFullscreen";
import Eye from "./Eye";
import Fullscreen from "./Fullscreen";
import Import from "./Import";
import LeftOutlined from "./LeftOutlined";
import LightMySpcae from "./LightMySpcae";
import LightPlayground from "./LightPlayground";
import List from "./List";
import Local from "./Local";
import LocalMcp from "./LocalMcp";
import Mcp from "./Mcp";
import McpAvatar from "./McpAvatar";
import More from "./More";
import MySpcae from "./MySpcae";
import OffcialExample from "./OffcialExample";
import Ops from "./Ops";
import OpsBg from "./OpsBg";
import OpsBg1 from "./OpsBg1";
import OpsBg2 from "./OpsBg2";
import Organization from "./Organization";
import Params from "./Params";
import Playground from "./Playground";
import PlaygroundConfig from "./PlaygroundConfig";
import Refresh from "./Refresh";
import RefreshTools from "./RefreshTools";
import Remote from "./Remote";
import RemoteMcp from "./RemoteMcp";
import Result from "./Result";
import RightArrow from "./RightArrow";
import Send from "./Send";
import Setting from "./Setting";
import ShowMore from "./ShowMore";
import SortAsc from "./SortAsc";
import SortDesc from "./SortDesc";
import Sse from "./Sse";
import Standard from "./Standard";
import Stdio from "./Stdio";
import StdioMcp from "./StdioMcp";
import Step01 from "./Step01";
import Step02 from "./Step02";
import Step03 from "./Step03";
import Step04 from "./Step04";
import StopGenerate from "./StopGenerate";
import Subscribe from "./Subscribe";
import Subscribe2 from "./Subscribe2";
import SubscribeFilled from "./SubscribeFilled";
import Subtract from "./Subtract";
import Tag from "./Tag";
import Test from "./Test";
import TestBg from "./TestBg";
import TestBg1 from "./TestBg1";
import TestBg2 from "./TestBg2";
import Tool from "./Tool";
import Unfold from "./Unfold";
import UpOutlined from "./UpOutlined";
import Vip from "./Vip";

export const IconClose = createIcon(Close);
export const IconIconInfoflow = createIcon(IconInfoflow);
export const IconIconOutlook = createIcon(IconOutlook);
export const IconSendActived = createIcon(SendActived);
export const IconAiTools1 = createIcon(AiTools1);
export const IconAiTools2 = createIcon(AiTools2);
export const IconAiTools3 = createIcon(AiTools3);
export const IconAiTools4 = createIcon(AiTools4);
export const IconAlert = createIcon(Alert);
export const IconAnnounceAlert = createIcon(AnnounceAlert);
export const IconArrowRight = createIcon(ArrowRight);
export const IconArrowRight1 = createIcon(ArrowRight1);
export const IconCallCount = createIcon(CallCount);
export const IconCase = createIcon(Case);
export const IconComment = createIcon(Comment);
export const IconCopy = createIcon(Copy);
export const IconDebug = createIcon(Debug);
export const IconDelete = createIcon(Delete);
export const IconDetail = createIcon(Detail);
export const IconDev = createIcon(Dev);
export const IconDevBg = createIcon(DevBg);
export const IconDevBg1 = createIcon(DevBg1);
export const IconDevBg2 = createIcon(DevBg2);
export const IconDownOutlined = createIcon(DownOutlined);
export const IconElipsis = createIcon(Elipsis);
export const IconExitFullscreen = createIcon(ExitFullscreen);
export const IconEye = createIcon(Eye);
export const IconFullscreen = createIcon(Fullscreen);
export const IconImport = createIcon(Import);
export const IconLeftOutlined = createIcon(LeftOutlined);
export const IconLightMySpcae = createIcon(LightMySpcae);
export const IconLightPlayground = createIcon(LightPlayground);
export const IconList = createIcon(List);
export const IconLocal = createIcon(Local);
export const IconLocalMcp = createIcon(LocalMcp);
export const IconMcp = createIcon(Mcp);
export const IconMcpAvatar = createIcon(McpAvatar);
export const IconMore = createIcon(More);
export const IconMySpcae = createIcon(MySpcae);
export const IconOffcialExample = createIcon(OffcialExample);
export const IconOps = createIcon(Ops);
export const IconOpsBg = createIcon(OpsBg);
export const IconOpsBg1 = createIcon(OpsBg1);
export const IconOpsBg2 = createIcon(OpsBg2);
export const IconOrganization = createIcon(Organization);
export const IconParams = createIcon(Params);
export const IconPlayground = createIcon(Playground);
export const IconPlaygroundConfig = createIcon(PlaygroundConfig);
export const IconRefresh = createIcon(Refresh);
export const IconRefreshTools = createIcon(RefreshTools);
export const IconRemote = createIcon(Remote);
export const IconRemoteMcp = createIcon(RemoteMcp);
export const IconResult = createIcon(Result);
export const IconRightArrow = createIcon(RightArrow);
export const IconSend = createIcon(Send);
export const IconSetting = createIcon(Setting);
export const IconShowMore = createIcon(ShowMore);
export const IconSortAsc = createIcon(SortAsc);
export const IconSortDesc = createIcon(SortDesc);
export const IconSse = createIcon(Sse);
export const IconStandard = createIcon(Standard);
export const IconStdio = createIcon(Stdio);
export const IconStdioMcp = createIcon(StdioMcp);
export const IconStep01 = createIcon(Step01);
export const IconStep02 = createIcon(Step02);
export const IconStep03 = createIcon(Step03);
export const IconStep04 = createIcon(Step04);
export const IconStopGenerate = createIcon(StopGenerate);
export const IconSubscribe = createIcon(Subscribe);
export const IconSubscribe2 = createIcon(Subscribe2);
export const IconSubscribeFilled = createIcon(SubscribeFilled);
export const IconSubtract = createIcon(Subtract);
export const IconTag = createIcon(Tag);
export const IconTest = createIcon(Test);
export const IconTestBg = createIcon(TestBg);
export const IconTestBg1 = createIcon(TestBg1);
export const IconTestBg2 = createIcon(TestBg2);
export const IconTool = createIcon(Tool);
export const IconUnfold = createIcon(Unfold);
export const IconUpOutlined = createIcon(UpOutlined);
export const IconVip = createIcon(Vip);

export const iconsMap: Record<string, FC<SVGProps<SVGSVGElement>>> = {
    Close: IconClose,
    IconInfoflow: IconIconInfoflow,
    IconOutlook: IconIconOutlook,
    SendActived: IconSendActived,
    aiTools1: IconAiTools1,
    aiTools2: IconAiTools2,
    aiTools3: IconAiTools3,
    aiTools4: IconAiTools4,
    alert: IconAlert,
    announceAlert: IconAnnounceAlert,
    arrowRight: IconArrowRight,
    arrowRight1: IconArrowRight1,
    callCount: IconCallCount,
    case: IconCase,
    comment: IconComment,
    copy: IconCopy,
    debug: IconDebug,
    delete: IconDelete,
    detail: IconDetail,
    dev: IconDev,
    devBg: IconDevBg,
    devBg1: IconDevBg1,
    devBg2: IconDevBg2,
    downOutlined: IconDownOutlined,
    elipsis: IconElipsis,
    exitFullscreen: IconExitFullscreen,
    eye: IconEye,
    fullscreen: IconFullscreen,
    import: IconImport,
    leftOutlined: IconLeftOutlined,
    lightMySpcae: IconLightMySpcae,
    lightPlayground: IconLightPlayground,
    list: IconList,
    local: IconLocal,
    localMCP: IconLocalMcp,
    mcp: IconMcp,
    mcpAvatar: IconMcpAvatar,
    more: IconMore,
    mySpcae: IconMySpcae,
    offcialExample: IconOffcialExample,
    ops: IconOps,
    opsBg: IconOpsBg,
    opsBg1: IconOpsBg1,
    opsBg2: IconOpsBg2,
    organization: IconOrganization,
    params: IconParams,
    playground: IconPlayground,
    playgroundConfig: IconPlaygroundConfig,
    refresh: IconRefresh,
    refreshTools: IconRefreshTools,
    remote: IconRemote,
    remoteMCP: IconRemoteMcp,
    result: IconResult,
    rightArrow: IconRightArrow,
    send: IconSend,
    setting: IconSetting,
    showMore: IconShowMore,
    sortAsc: IconSortAsc,
    sortDesc: IconSortDesc,
    sse: IconSse,
    standard: IconStandard,
    stdio: IconStdio,
    stdioMCP: IconStdioMcp,
    step01: IconStep01,
    step02: IconStep02,
    step03: IconStep03,
    step04: IconStep04,
    stopGenerate: IconStopGenerate,
    subscribe: IconSubscribe,
    subscribe2: IconSubscribe2,
    subscribeFilled: IconSubscribeFilled,
    subtract: IconSubtract,
    tag: IconTag,
    test: IconTest,
    testBg: IconTestBg,
    testBg1: IconTestBg1,
    testBg2: IconTestBg2,
    tool: IconTool,
    unfold: IconUnfold,
    upOutlined: IconUpOutlined,
    vip: IconVip,
};

export default iconsMap;
