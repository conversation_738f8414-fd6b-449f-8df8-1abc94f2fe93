import styled from '@emotion/styled';
import {Flex, Toolt<PERSON>, Typography} from 'antd';
import {useCallback, useMemo} from 'react';
import {Button} from '@panda-design/components';
import {Markdown} from '@/design/Markdown';
import {useMCPServerId} from '@/components/MCP/hooks';
import {useMCPServer} from '@/regions/mcp/mcpServer';
import <PERSON><PERSON><PERSON><PERSON>ie<PERSON> from '@/components/MCP/JSONViewer';
import {MCPTrackActions} from '@/api/mcp/track';

const Container = styled(Flex)`
    flex: 1;
    display: grid;
    grid-template-columns: 6fr 4fr;
    gap: 16px;
`;

const Content = styled.div`
    border-radius: 6px;
    border: 1px solid #E8E8E8;
    padding: 16px 24px;
    flex: 1;
    overflow: auto;
    ::-webkit-scrollbar {
        display: none;
    }
`;


const MCPOverview = () => {

    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);

    const config = useMemo(
        () => {
            if (mcpServer?.serverConf?.serverConfig) {
                try {
                    return JSON.parse(mcpServer?.serverConf?.serverConfig);
                } catch (e) {
                    return mcpServer?.serverConf?.serverConfig;
                }
                // return JSON.parse(mcpServer?.serverConf?.serverConfig);
            }
        },
        [mcpServer?.serverConf?.serverConfig]
    );
    const onCopy = useCallback(
        () => {
            MCPTrackActions.copyServerConfig([mcpServerId]);
        },
        [mcpServerId]
    );
    return (
        <Container>
            <Content>
                <Markdown content={mcpServer?.serverConf?.overview || ''} codeHighlight />
            </Content>
            <Content>
                <Flex vertical gap={12}>
                    <Flex justify="space-between" align="center">
                        <Typography.Title level={4}>Server Config</Typography.Title>
                        <Tooltip title="重新获取工具列表">
                            <Button type="link">刷新</Button>
                        </Tooltip>
                    </Flex>
                    <JSONViewer json={config} onCopy={onCopy} />
                </Flex>
            </Content>
        </Container>
    );
};

export default MCPOverview;
